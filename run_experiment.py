#!/usr/bin/env python3
"""
MultiChannelSleepNet 完全自动化实验脚本
使用 sleepEDF-78 数据集，支持GPU加速，详细日志记录

作者: AI Assistant
日期: 2025-01-08
"""

import os
import sys
import time
import subprocess
import logging
import datetime
from pathlib import Path
import torch
import numpy as np


class MultiChannelSleepNetRunner:
    def __init__(self):
        self.project_root = Path("/media/main/ypf/eeg/MultiChannelSleepNet")
        self.logs_dir = self.project_root / "logs"
        self.dataset_name = "sleepEDF-78"
        self.start_time = datetime.datetime.now()

        # 创建日志目录
        self.logs_dir.mkdir(exist_ok=True)

        # 设置主日志
        self.setup_main_logger()

        # 阶段信息
        self.stages = [
            {
                "name": "数据预处理",
                "script": "dataset_prepare.py",
                "est_time": "10分钟",
            },
            {
                "name": "时频转换",
                "script": "data_preprocess_TF.py",
                "est_time": "15分钟",
            },
            {"name": "模型训练", "script": "Kfold_trainer.py", "est_time": "2-4小时"},
            {"name": "结果评估", "script": "result_evaluate.py", "est_time": "5分钟"},
        ]

    def setup_main_logger(self):
        """设置主日志记录器"""
        timestamp = self.start_time.strftime("%Y%m%d_%H%M%S")
        log_file = self.logs_dir / f"run_{timestamp}.log"

        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(log_file, encoding="utf-8"),
                logging.StreamHandler(sys.stdout),
            ],
        )
        self.logger = logging.getLogger(__name__)

    def print_banner(self):
        """打印启动横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                MultiChannelSleepNet 自动化实验               ║
║                                                              ║
║  数据集: sleepEDF-78 (78个受试者)                           ║
║  模式: 完全自动化执行                                        ║
║  GPU: 已启用                                                ║
║  日志目录: /media/main/ypf/eeg/MultiChannelSleepNet/logs    ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        self.logger.info("MultiChannelSleepNet 自动化实验开始")

    def check_environment(self):
        """检查运行环境"""
        self.logger.info("🔍 检查运行环境...")

        # 检查Python版本
        python_version = sys.version_info
        self.logger.info(
            f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}"
        )

        # 检查CUDA和GPU
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            self.logger.info(f"✅ GPU可用: {gpu_count}个GPU, 主GPU: {gpu_name}")

            # 保存GPU信息到系统日志
            with open(self.logs_dir / "system_info.log", "w", encoding="utf-8") as f:
                f.write(f"GPU信息:\n")
                f.write(f"GPU数量: {gpu_count}\n")
                f.write(f"主GPU: {gpu_name}\n")
                f.write(f"CUDA版本: {torch.version.cuda}\n")
                f.write(f"PyTorch版本: {torch.__version__}\n")
        else:
            self.logger.warning("⚠️ GPU不可用，将使用CPU运行（速度较慢）")

        # 检查关键依赖
        try:
            import mne
            import scipy
            import sklearn
            import tqdm

            self.logger.info("✅ 关键依赖包检查通过")
        except ImportError as e:
            self.logger.error(f"❌ 缺少依赖包: {e}")
            return False

        return True

    def verify_dataset(self):
        """验证数据集完整性"""
        self.logger.info("🔍 验证sleepEDF-78数据集...")

        dataset_path = (
            self.project_root / "dataset" / self.dataset_name / "sleep-cassette"
        )

        if not dataset_path.exists():
            self.logger.error(f"❌ 数据集目录不存在: {dataset_path}")
            return False

        # 检查文件数量
        edf_files = list(dataset_path.glob("*.edf"))
        psg_files = [f for f in edf_files if "PSG" in f.name]
        hypno_files = [f for f in edf_files if "Hypnogram" in f.name]

        self.logger.info(f"PSG文件数量: {len(psg_files)}")
        self.logger.info(f"Hypnogram文件数量: {len(hypno_files)}")

        # 检查PSG和Hypnogram文件数量是否匹配
        if len(psg_files) != len(hypno_files):
            self.logger.error(
                f"❌ PSG文件数量({len(psg_files)})与Hypnogram文件数量({len(hypno_files)})不匹配"
            )
            return False

        # 更新数据集信息
        actual_subjects = len(psg_files)
        self.logger.info(f"✅ 数据集验证通过: 发现{actual_subjects}个受试者的完整数据")

        # 更新横幅信息中的受试者数量
        if actual_subjects != 78:
            self.logger.info(
                f"📝 注意: 实际数据集包含{actual_subjects}个受试者，而非标准的78个"
            )
        return True

    def ensure_correct_config(self):
        """确保args.py配置正确"""
        self.logger.info("🔧 配置args.py为sleepEDF-78...")

        args_file = self.project_root / "args.py"

        # 读取当前配置
        with open(args_file, "r", encoding="utf-8") as f:
            content = f.read()

        # 检查是否已经是sleepEDF-78配置
        if "sleepEDF-78" in content:
            self.logger.info("✅ args.py已配置为sleepEDF-78")
        else:
            self.logger.info("⚠️ args.py需要更新为sleepEDF-78配置")
            # 这里可以添加自动修改逻辑，但为了安全起见，先记录

    def run_stage(self, stage_info, stage_num):
        """执行单个阶段"""
        stage_name = stage_info["name"]
        script_name = stage_info["script"]
        est_time = stage_info["est_time"]

        self.logger.info(f"🚀 阶段{stage_num}: {stage_name} (预估时间: {est_time})")
        self.logger.info(f"执行脚本: {script_name}")

        # 设置日志文件
        log_file = self.logs_dir / f"{script_name.replace('.py', '.log')}"

        # 记录开始时间
        stage_start = time.time()

        try:
            # 执行脚本并重定向输出到日志文件
            with open(log_file, "w", encoding="utf-8") as f:
                process = subprocess.Popen(
                    [sys.executable, script_name],
                    cwd=self.project_root,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                )

                # 等待完成
                return_code = process.wait()

            # 计算执行时间
            stage_duration = time.time() - stage_start
            duration_str = self.format_duration(stage_duration)

            if return_code == 0:
                self.logger.info(
                    f"✅ 阶段{stage_num}完成: {stage_name} (耗时: {duration_str})"
                )
                return True
            else:
                self.logger.error(
                    f"❌ 阶段{stage_num}失败: {stage_name} (返回码: {return_code})"
                )
                self.logger.error(f"详细错误信息请查看: {log_file}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 阶段{stage_num}执行异常: {e}")
            return False

    def format_duration(self, seconds):
        """格式化时间duration"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            return f"{seconds/60:.1f}分钟"
        else:
            return f"{seconds/3600:.1f}小时"

    def generate_summary_report(self):
        """生成实验总结报告"""
        total_duration = time.time() - self.start_time.timestamp()
        duration_str = self.format_duration(total_duration)

        summary = f"""
╔══════════════════════════════════════════════════════════════╗
║                    实验完成总结报告                          ║
╠══════════════════════════════════════════════════════════════╣
║ 数据集: {self.dataset_name}                                 ║
║ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}   ║
║ 结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ║
║ 总耗时: {duration_str}                                      ║
║                                                              ║
║ 输出文件位置:                                                ║
║ - 训练模型: ./Kfold_models/                                 ║
║ - 处理数据: ./data/{self.dataset_name}/                     ║
║ - 详细日志: ./logs/                                         ║
╚══════════════════════════════════════════════════════════════╝
        """

        print(summary)
        self.logger.info("实验完成总结报告已生成")

        # 保存到文件
        with open(self.logs_dir / "experiment_summary.txt", "w", encoding="utf-8") as f:
            f.write(summary)

    def run_experiment(self):
        """运行完整实验流程"""
        try:
            # 打印启动横幅
            self.print_banner()

            # 环境检查
            if not self.check_environment():
                self.logger.error("❌ 环境检查失败，实验终止")
                return False

            # 数据集验证
            if not self.verify_dataset():
                self.logger.error("❌ 数据集验证失败，实验终止")
                return False

            # 配置检查
            self.ensure_correct_config()

            # 执行各个阶段
            self.logger.info("🎯 开始执行实验阶段...")

            for i, stage in enumerate(self.stages, 1):
                if not self.run_stage(stage, i):
                    self.logger.error(f"❌ 实验在阶段{i}失败，终止执行")
                    return False

                # 阶段间短暂休息
                if i < len(self.stages):
                    self.logger.info("⏸️ 阶段间休息5秒...")
                    time.sleep(5)

            # 生成总结报告
            self.generate_summary_report()
            self.logger.info("🎉 实验成功完成！")
            return True

        except KeyboardInterrupt:
            self.logger.warning("⚠️ 用户中断实验")
            return False
        except Exception as e:
            self.logger.error(f"❌ 实验执行异常: {e}")
            return False


def main():
    """主函数"""
    print("正在初始化MultiChannelSleepNet自动化实验...")

    # 切换到项目目录
    project_root = Path("/media/main/ypf/eeg/MultiChannelSleepNet")
    os.chdir(project_root)

    # 创建并运行实验
    runner = MultiChannelSleepNetRunner()
    success = runner.run_experiment()

    if success:
        print("\n🎉 实验成功完成！请查看logs目录获取详细信息。")
        sys.exit(0)
    else:
        print("\n❌ 实验失败！请查看日志文件排查问题。")
        sys.exit(1)


if __name__ == "__main__":
    main()
