2025-08-06 00:44:20,709 - INFO - MultiChannelSleepNet 自动化实验开始
2025-08-06 00:44:20,710 - INFO - 🔍 检查运行环境...
2025-08-06 00:44:20,710 - INFO - Python版本: 3.9.23
2025-08-06 00:44:20,746 - INFO - ✅ GPU可用: 2个GPU, 主GPU: NVIDIA GeForce RTX 4090
2025-08-06 00:44:21,457 - INFO - ✅ 关键依赖包检查通过
2025-08-06 00:44:21,457 - INFO - 🔍 验证sleepEDF-78数据集...
2025-08-06 00:44:21,459 - INFO - PSG文件数量: 153
2025-08-06 00:44:21,459 - INFO - Hypnogram文件数量: 153
2025-08-06 00:44:21,459 - INFO - ✅ 数据集验证通过: 发现153个睡眠记录
2025-08-06 00:44:21,459 - INFO - 📝 标准SleepEDF-78数据集: 78个受试者，153个睡眠记录（多数受试者有2晚数据）
2025-08-06 00:44:21,459 - INFO - 🔧 配置args.py为sleepEDF-78...
2025-08-06 00:44:21,459 - INFO - ✅ args.py已配置为sleepEDF-78
2025-08-06 00:44:21,460 - INFO - 🎯 开始执行实验阶段...
2025-08-06 00:44:21,460 - INFO - 🚀 阶段1: 数据预处理 (预估时间: 10分钟)
2025-08-06 00:44:21,460 - INFO - 执行脚本: dataset_prepare.py
