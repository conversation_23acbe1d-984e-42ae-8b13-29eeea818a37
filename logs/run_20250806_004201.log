2025-08-06 00:42:01,118 - INFO - MultiChannelSleepNet 自动化实验开始
2025-08-06 00:42:01,118 - INFO - 🔍 检查运行环境...
2025-08-06 00:42:01,118 - INFO - Python版本: 3.9.23
2025-08-06 00:42:01,153 - INFO - ✅ GPU可用: 2个GPU, 主GPU: NVIDIA GeForce RTX 4090
2025-08-06 00:42:01,795 - INFO - ✅ 关键依赖包检查通过
2025-08-06 00:42:01,795 - INFO - 🔍 验证sleepEDF-78数据集...
2025-08-06 00:42:01,797 - INFO - PSG文件数量: 153
2025-08-06 00:42:01,797 - INFO - Hypnogram文件数量: 153
2025-08-06 00:42:01,798 - INFO - ✅ 数据集验证通过: 发现153个受试者的完整数据
2025-08-06 00:42:01,798 - INFO - 📝 注意: 实际数据集包含153个受试者，而非标准的78个
2025-08-06 00:42:01,798 - INFO - 🔧 配置args.py为sleepEDF-78...
2025-08-06 00:42:01,798 - INFO - ⚠️ args.py需要更新为sleepEDF-78配置
2025-08-06 00:42:01,798 - INFO - 🎯 开始执行实验阶段...
2025-08-06 00:42:01,798 - INFO - 🚀 阶段1: 数据预处理 (预估时间: 10分钟)
2025-08-06 00:42:01,798 - INFO - 执行脚本: dataset_prepare.py
